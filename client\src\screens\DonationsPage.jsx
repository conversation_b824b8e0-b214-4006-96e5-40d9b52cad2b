import React, { useState, useRef, useEffect } from "react";
import { View, Text, StyleSheet, TextInput, ScrollView, Animated, TouchableOpacity } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import Header from "../components/Header";
import AnimatedButton from "../components/AnimatedButton";
import { useAuth } from "../context/AuthContext";
import { createFadeInAnimation, createStaggerAnimation } from "../utils/animations";
import colors from "../styles/colors";
import apiService from "../services/apiService";

export default function DonationsPage() {
  const [donorName, setDonorName] = useState("");
  const [address, setAddress] = useState("");
  const [phone, setPhone] = useState("");
  const [amount, setAmount] = useState("");
  const [donationType, setDonationType] = useState("Cash");
  const [notes, setNotes] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Validation states
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [toastType, setToastType] = useState("success"); // success, error, info

  const navigation = useNavigation();
  const { user, isAdmin } = useAuth();

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const inputAnims = useRef([
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
  ]).current;
  const toastAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Fade in the header first, then stagger the inputs
    Animated.sequence([
      createFadeInAnimation(fadeAnim, 400),
      createStaggerAnimation(inputAnims, 150),
    ]).start();
  }, [fadeAnim, inputAnims]);

  // Toast functionality
  const showToastMessage = (message, type = "success") => {
    setToastMessage(message);
    setToastType(type);
    setShowToast(true);

    // Animate toast in
    Animated.sequence([
      Animated.timing(toastAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.delay(3000),
      Animated.timing(toastAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setShowToast(false);
    });
  };

  // Real-time validation functions
  const validateField = (field, value) => {
    let error = "";

    switch (field) {
      case "donorName":
        if (!value.trim()) {
          error = "Donor name is required";
        } else if (value.trim().length < 2) {
          error = "Donor name must be at least 2 characters";
        }
        break;

      case "address":
        if (!value.trim()) {
          error = "Address is required";
        } else if (value.trim().length < 10) {
          error = "Please enter a complete address";
        }
        break;

      case "phone":
        if (!value.trim()) {
          error = "Phone number is required";
        } else {
          const cleanPhone = value.replace(/[^0-9]/g, '');
          if (cleanPhone.length < 10) {
            error = "Phone number must be at least 10 digits";
          } else if (cleanPhone.length > 15) {
            error = "Phone number cannot exceed 15 digits";
          }
        }
        break;

      case "amount":
        if (!value.trim()) {
          error = "Amount is required";
        } else {
          const numericAmount = parseFloat(value);
          if (isNaN(numericAmount)) {
            error = "Please enter a valid number";
          } else if (numericAmount <= 0) {
            error = "Amount must be greater than 0";
          } else if (numericAmount > 1000000) {
            error = "Amount cannot exceed ₹10,00,000";
          }
        }
        break;

      case "donationType":
        if (!value.trim()) {
          error = "Donation type is required";
        }
        break;
    }

    return error;
  };

  const handleFieldChange = (field, value) => {
    // Update the field value
    switch (field) {
      case "donorName":
        setDonorName(value);
        break;
      case "address":
        setAddress(value);
        break;
      case "phone":
        setPhone(value);
        break;
      case "amount":
        setAmount(value);
        break;
      case "donationType":
        setDonationType(value);
        break;
      case "notes":
        setNotes(value);
        break;
    }

    // Mark field as touched
    setTouched(prev => ({ ...prev, [field]: true }));

    // Validate field and update errors
    const error = validateField(field, value);
    setErrors(prev => ({ ...prev, [field]: error }));
  };

  const validateForm = () => {
    const fields = ["donorName", "address", "phone", "amount", "donationType"];
    const values = { donorName, address, phone, amount, donationType };
    const newErrors = {};
    let hasErrors = false;

    // Validate all fields
    fields.forEach(field => {
      const error = validateField(field, values[field]);
      if (error) {
        newErrors[field] = error;
        hasErrors = true;
      }
    });

    // Mark all fields as touched
    const allTouched = {};
    fields.forEach(field => {
      allTouched[field] = true;
    });
    setTouched(allTouched);
    setErrors(newErrors);

    if (hasErrors) {
      showToastMessage("Please fix the errors below", "error");
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const donationData = {
        donorName: donorName.trim(),
        donorAddress: address.trim(),
        donorPhone: phone.trim(),
        donationAmount: parseFloat(amount),
        donationType: donationType || "Cash",
        notes: notes.trim() || null,
      };

      console.log('Submitting donation:', donationData);
      const result = await apiService.createDonation(donationData);

      if (result.success) {
        showToastMessage(
          `Donation submitted successfully! ₹${amount} from ${donorName}`,
          "success"
        );

        // Clear form after successful submission
        setDonorName("");
        setAddress("");
        setPhone("");
        setAmount("");
        setDonationType("Cash");
        setNotes("");
        setErrors({});
        setTouched({});
      } else {
        showToastMessage(result.message || "Failed to submit donation", "error");
      }
    } catch (error) {
      console.error('Donation submission error:', error);
      showToastMessage("Failed to submit donation. Please check your connection and try again.", "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper component for input fields with validation
  const ValidatedInput = ({
    field,
    value,
    placeholder,
    icon,
    keyboardType = "default",
    multiline = false,
    animationIndex = 0
  }) => {
    const hasError = touched[field] && errors[field];
    const isValid = touched[field] && !errors[field] && value.trim();

    return (
      <Animated.View style={[styles.inputContainer, { opacity: inputAnims[animationIndex] }]}>
        <View style={[
          styles.inputWrapper,
          hasError && styles.inputWrapperError,
          isValid && styles.inputWrapperValid
        ]}>
          <Ionicons
            name={icon}
            size={20}
            color={hasError ? colors.danger : isValid ? colors.success : colors.textLight}
            style={styles.icon}
          />
          <TextInput
            style={[
              styles.input,
              multiline && styles.multilineInput,
              hasError && styles.inputError,
              isValid && styles.inputValid
            ]}
            placeholder={placeholder}
            value={value}
            onChangeText={(text) => handleFieldChange(field, text)}
            onBlur={() => setTouched(prev => ({ ...prev, [field]: true }))}
            keyboardType={keyboardType}
            multiline={multiline}
            placeholderTextColor={colors.textMuted}
          />
          {hasError && (
            <Ionicons
              name="alert-circle"
              size={20}
              color={colors.danger}
              style={styles.validationIcon}
            />
          )}
          {isValid && (
            <Ionicons
              name="checkmark-circle"
              size={20}
              color={colors.success}
              style={styles.validationIcon}
            />
          )}
        </View>
        {hasError && (
          <Text style={styles.errorText}>{errors[field]}</Text>
        )}
      </Animated.View>
    );
  };

  // Toast component
  const ToastComponent = () => {
    if (!showToast) return null;

    const toastStyle = {
      backgroundColor: toastType === "success" ? colors.success :
                      toastType === "error" ? colors.danger : colors.info,
    };

    return (
      <Animated.View
        style={[
          styles.toast,
          toastStyle,
          {
            opacity: toastAnim,
            transform: [{
              translateY: toastAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [-100, 0],
              }),
            }],
          }
        ]}
      >
        <Ionicons
          name={toastType === "success" ? "checkmark-circle" :
                toastType === "error" ? "alert-circle" : "information-circle"}
          size={20}
          color={colors.cardBg}
          style={styles.toastIcon}
        />
        <Text style={styles.toastText}>{toastMessage}</Text>
        <TouchableOpacity
          onPress={() => {
            Animated.timing(toastAnim, {
              toValue: 0,
              duration: 200,
              useNativeDriver: true,
            }).start(() => setShowToast(false));
          }}
          style={styles.toastCloseButton}
        >
          <Ionicons name="close" size={18} color={colors.cardBg} />
        </TouchableOpacity>
      </Animated.View>
    );
  };

  return (
    <View style={styles.container}>
      <Header title="Donations" />
      <ToastComponent />
      <ScrollView contentContainerStyle={styles.content}>
        <Animated.View style={[styles.headerSection, { opacity: fadeAnim }]}>
          <Text style={styles.heading}>Donation Form</Text>
          <Text style={styles.subheading}>
            Welcome, {user?.name}! Please fill in the donation details below.
          </Text>
        </Animated.View>

        {/* Donor Name */}
        <ValidatedInput
          field="donorName"
          value={donorName}
          placeholder="Donor Name"
          icon="person-outline"
          animationIndex={0}
        />

        {/* Address */}
        <ValidatedInput
          field="address"
          value={address}
          placeholder="Address"
          icon="home-outline"
          multiline={true}
          animationIndex={1}
        />

        {/* Phone */}
        <ValidatedInput
          field="phone"
          value={phone}
          placeholder="Phone Number"
          icon="call-outline"
          keyboardType="phone-pad"
          animationIndex={2}
        />

        {/* Amount */}
        <ValidatedInput
          field="amount"
          value={amount}
          placeholder="Amount (₹)"
          icon="cash-outline"
          keyboardType="numeric"
          animationIndex={3}
        />

        {/* Donation Type */}
        <ValidatedInput
          field="donationType"
          value={donationType}
          placeholder="Donation Type (Cash, Cheque, Online, etc.)"
          icon="card-outline"
          animationIndex={4}
        />

        {/* Notes */}
        <ValidatedInput
          field="notes"
          value={notes}
          placeholder="Notes (optional)"
          icon="document-text-outline"
          multiline={true}
          animationIndex={5}
        />

        {/* Submit Button */}
        <AnimatedButton
          title="Submit Donation"
          onPress={handleSubmit}
          loading={isSubmitting}
          disabled={isSubmitting}
          variant="primary"
          size="large"
          style={styles.submitButton}
          icon={<Ionicons name="heart" size={20} color={colors.cardBg} style={{ marginRight: 8 }} />}
        />

        {/* View Donations Button */}
        <AnimatedButton
          title="View Donations"
          onPress={() => navigation.navigate('DonationList')}
          variant="secondary"
          size="large"
          style={styles.viewButton}
          icon={<Ionicons name="list" size={20} color={colors.primary} style={{ marginRight: 8 }} />}
        />

        {/* Quick Actions for Admin */}
        {isAdmin() && (
          <View style={styles.adminActions}>
            <Text style={styles.adminTitle}>Admin Actions</Text>
            <AnimatedButton
              title="Go to Dashboard"
              onPress={() => navigation.navigate('Home')}
              variant="secondary"
              size="medium"
              style={styles.adminButton}
              icon={<Ionicons name="speedometer" size={18} color={colors.cardBg} style={{ marginRight: 6 }} />}
            />
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background
  },
  content: {
    padding: 24,
    alignItems: "center",
  },
  headerSection: {
    alignItems: "center",
    marginBottom: 32,
    paddingHorizontal: 16,
  },
  heading: {
    fontSize: 28,
    fontWeight: "800",
    marginBottom: 12,
    color: colors.textDark,
    textAlign: "center",
  },
  subheading: {
    fontSize: 16,
    color: colors.textMedium,
    textAlign: "center",
    lineHeight: 22,
    marginBottom: 8,
  },
  inputContainer: {
    marginBottom: 20,
    width: "100%",
  },
  inputWrapper: {
    flexDirection: "row",
    alignItems: "flex-start",
    borderWidth: 2,
    borderColor: colors.border,
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 4,
    backgroundColor: colors.cardBg,
    shadowColor: colors.shadowMedium,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 3,
  },
  inputWrapperError: {
    borderColor: colors.danger,
    backgroundColor: colors.dangerSoft,
  },
  inputWrapperValid: {
    borderColor: colors.success,
    backgroundColor: colors.successSoft,
  },
  icon: {
    marginRight: 12,
    marginTop: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 12,
    color: colors.textDark,
    fontWeight: "500",
  },
  inputError: {
    color: colors.danger,
  },
  inputValid: {
    color: colors.success,
  },
  multilineInput: {
    minHeight: 80,
    textAlignVertical: 'top',
    paddingTop: 12,
  },
  validationIcon: {
    marginLeft: 8,
    marginTop: 12,
  },
  errorText: {
    color: colors.danger,
    fontSize: 12,
    marginTop: 4,
    marginLeft: 36,
    fontWeight: "500",
  },
  toast: {
    position: 'absolute',
    top: 100,
    left: 20,
    right: 20,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    zIndex: 1000,
    shadowColor: colors.shadowDark,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 1,
    shadowRadius: 8,
    elevation: 8,
  },
  toastIcon: {
    marginRight: 12,
  },
  toastText: {
    flex: 1,
    color: colors.cardBg,
    fontSize: 14,
    fontWeight: "600",
  },
  toastCloseButton: {
    padding: 4,
    marginLeft: 8,
  },
  submitButton: {
    marginTop: 24,
    width: "100%",
  },
  viewButton: {
    marginTop: 16,
    width: "100%",
  },
  adminActions: {
    marginTop: 40,
    width: "100%",
    alignItems: "center",
    paddingTop: 24,
    borderTopWidth: 2,
    borderTopColor: colors.borderLight,
    backgroundColor: colors.surfaceElevated,
    borderRadius: 16,
    padding: 20,
    shadowColor: colors.shadowLight,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 2,
  },
  adminTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: colors.textDark,
    marginBottom: 16,
    textAlign: "center",
  },
  adminButton: {
    width: "100%",
  },
});
